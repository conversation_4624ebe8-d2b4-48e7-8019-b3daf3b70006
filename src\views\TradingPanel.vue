<template>
  <div class="trading-panel-container">
    <!-- 主交易面板 -->
    <div class="trading-panel">
      <!-- 左侧操作列 -->
      <div class="left-control-panel">
        <!-- 合约代码显示 -->
        <div class="contract-code-display">
          {{ currentContract?.code || panelContract?.code || '未选择' }}
        </div>

        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn">-</button>
          <button @click="zoomIn" class="zoom-btn">+</button>
        </div>

        <!-- 交易操作按钮 -->
        <div class="trading-controls">
          <button @click="placeTestOrder" class="order-btn place-btn" title="下单">
            下单
          </button>
          <button @click="cancelTestOrder" class="order-btn cancel-btn" title="撤单" :disabled="!hasActiveOrder">
            撤单
          </button>
        </div>

        <!-- 合约详情信息 -->
        <div class="contract-details" v-if="getDisplayContract()">
          <div class="contract-title">{{ getDisplayContract().name }}</div>
          <div class="contract-info-grid">
            <div class="info-row">
              <span class="label">代码:</span>
              <span class="value">{{ getDisplayContract().code }}</span>
            </div>
            <div class="info-row">
              <span class="label">交易所:</span>
              <span class="value">{{ getDisplayContract().exchange || 'SHFE' }}</span>
            </div>
            <div class="info-row">
              <span class="label">最新价:</span>
              <span class="value price">{{ getDisplayContract().lastPrice || '--' }}</span>
            </div>
            <div class="info-row">
              <span class="label">涨跌幅:</span>
              <span :class="['value', 'change', getChangeClass(getDisplayContract().changePercent)]">
                {{ formatChangePercent(getDisplayContract().changePercent) }}
              </span>
            </div>
            <div class="info-row">
              <span class="label">成交量:</span>
              <span class="value">{{ formatVolume(getDisplayContract().volume) }}</span>
            </div>
            <div class="info-row">
              <span class="label">持仓量:</span>
              <span class="value">{{ formatVolume(getDisplayContract().openInterest) }}</span>
            </div>
            <div class="info-row">
              <span class="label">合约乘数:</span>
              <span class="value">{{ getDisplayContract().multiplier || 10 }}</span>
            </div>
            <div class="info-row">
              <span class="label">最小变动:</span>
              <span class="value">{{ getDisplayContract().tickSize || 1 }}</span>
            </div>
          </div>

          <!-- 五档行情 -->
          <div class="market-depth" v-if="getDisplayContract()">
            <div class="depth-title">五档行情</div>
            <div class="depth-item ask">
              <span class="depth-label">卖一:</span>
              <span class="depth-price ask-price">{{ getDisplayContract().ask1 || '--' }}</span>
              <span class="depth-volume">({{ getDisplayContract().askVolume1 || 0 }})</span>
            </div>
            <div class="depth-item bid">
              <span class="depth-label">买一:</span>
              <span class="depth-price bid-price">{{ getDisplayContract().bid1 || '--' }}</span>
              <span class="depth-volume">({{ getDisplayContract().bidVolume1 || 0 }})</span>
            </div>
            <div class="depth-note">
              <span class="note-text">下单将使用买一价排队</span>
            </div>
          </div>

          <!-- 当前订单状态 -->
          <div class="order-status" v-if="hasActiveOrder">
            <div class="status-title">当前订单</div>
            <div class="status-item">
              <span class="status-label">引用:</span>
              <span class="status-value">{{ lastOrderRef }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">状态:</span>
              <span :class="['status-value', 'status-' + orderStatus]">{{ getOrderStatusText(orderStatus) }}</span>
            </div>
            <div class="status-item" v-if="orderDetails">
              <span class="status-label">价格:</span>
              <span class="status-value">{{ orderDetails.price }}</span>
            </div>
            <div class="status-item" v-if="orderDetails">
              <span class="status-label">时间:</span>
              <span class="status-value">{{ orderDetails.submitTime }}</span>
            </div>
          </div>
        </div>

        <!-- 价格变化信息 -->
        <div class="price-info-section">
          <div class="price-change-display">
            <div class="price-change-value" :class="{ negative: priceChangePercent.startsWith('-'), positive: !priceChangePercent.startsWith('-') && priceChangePercent !== '' }">{{ priceChangePercent || '0.00' }}%</div>
          </div>

          <div class="market-stats">
            <div class="stat-item">{{ totalVolume || 0 }}</div>
            <div class="stat-item">{{ totalPosition || 0 }}</div>
            <div class="stat-item">{{ dailyPositionChange || 0 }}</div>
          </div>
        </div>

        <!-- 红蓝数值显示 -->
        <div class="zero-values">
          <div class="zero-value red">{{ redValue || 0 }}</div>
          <div class="zero-value blue">{{ blueValue || 0 }}</div>
        </div>

        <!-- 下单数量输入 -->
        <div class="order-inputs">
          <div class="input-group">
            <input v-model="lightOrderQuantity" type="number" class="order-input" placeholder="1" min="1" max="5"
                   title="轻仓下单数量（手）- 鼠标左键下单" />
          </div>
          <div class="input-group">
            <input v-model="heavyOrderQuantity" type="number" class="order-input" placeholder="20" min="5" max="25"
                   title="重仓下单数量（手）- 鼠标右键下单" />
          </div>
        </div>

        <!-- 订单类型选择 -->
        <div class="order-type-group">
          <label class="radio-label">
            <input type="radio" v-model="orderType" value="A" />
            <span class="radio-text">Order(A)</span>
          </label>
          <label class="radio-label">
            <input type="radio" v-model="orderType" value="B" />
            <span class="radio-text">Order(B)</span>
          </label>
        </div>

        <!-- 交易选项 -->
        <div class="order-options">
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.autoHand" />
            <span class="checkbox-text">金手指！</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.cLimit345" />
            <span class="checkbox-text">CLimit 345</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.cLimit550" />
            <span class="checkbox-text">CLimit 550</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.noLimit" />
            <span class="checkbox-text">No Limit</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.noCombo" />
            <span class="checkbox-text">NoCombo</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.upLimit" />
            <span class="checkbox-text">UpLimit</span>
          </label>
        </div>

        <!-- 持仓信息 -->
        <div class="position-info-section">
          <div class="position-line" title="净持仓 = 多头持仓 - 空头持仓">净仓: {{ netPosition || 0 }}</div>
          <div class="position-line" title="C: 平仓相关持仓, T: 今日持仓">C: {{ cPosition || 0 }} T: {{ tPosition || 0 }}</div>
        </div>

        <!-- 盈亏显示 -->
        <div class="pnl-display">
          <div class="pnl-value">{{ pnlValue || 0 }}</div>
          <div class="pnl-letter">P</div>
        </div>

      </div>

      <!-- 右侧五列表格 -->
      <div class="price-table-container">
        <div class="price-table" ref="tableContainer">
          <!-- 卖盘数据区域（红色区域，在黑条上方） -->
          <div class="sell-orders-section">
            <div
              v-for="(item, index) in sellOrders"
              :key="`sell-${index}`"
              class="price-row sell-row"
            >
              <!-- 第一列：撤单 -->
              <div class="cancel-col"></div>

              <!-- 第二列：买量 -->
              <div
                class="buy-volume-col clickable"
                @click="handleOrderClick('buy', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('buy', item, index, $event)"
                :class="{ active: isSelected('sell', 'buy', index) }"
                title="左键轻仓买入，右键重仓买入"
              >
                {{ item.buyVolume || '' }}
              </div>

              <!-- 第三列：价格 -->
              <div class="price-col market-price">
                {{ item.price || '' }}
              </div>

              <!-- 第四列：卖量 -->
              <div
                class="sell-volume-col clickable"
                @click="handleOrderClick('sell', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('sell', item, index, $event)"
                :class="{ active: isSelected('sell', 'sell', index) }"
                title="左键轻仓卖出，右键重仓卖出"
              >
                {{ item.sellVolume || '' }}
              </div>

              <!-- 第五列：总量 -->
              <div class="total-volume-col">
                {{ getTotalVolume(item) }}
              </div>
            </div>
          </div>

          <!-- 中间分隔线 -->
          <div class="price-separator"></div>

          <!-- 买盘数据区域（蓝色区域，在黑条下方） -->
          <div class="buy-orders-section">
            <div
              v-for="(item, index) in buyOrders"
              :key="`buy-${index}`"
              class="price-row buy-row"
            >
              <!-- 第一列：撤单 -->
              <div class="cancel-col"></div>

              <!-- 第二列：买量 -->
              <div
                class="buy-volume-col clickable"
                @click="handleOrderClick('buy', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('buy', item, index, $event)"
                :class="{ active: isSelected('buy', 'buy', index) }"
                title="左键轻仓买入，右键重仓买入"
              >
                {{ item.buyVolume || '' }}
              </div>

              <!-- 第三列：价格 -->
              <div class="price-col market-price">
                {{ item.price || '' }}
              </div>

              <!-- 第四列：卖量 -->
              <div
                class="sell-volume-col clickable"
                @click="handleOrderClick('sell', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('sell', item, index, $event)"
                :class="{ active: isSelected('buy', 'sell', index) }"
                title="左键轻仓卖出，右键重仓卖出"
              >
                {{ item.sellVolume || '' }}
              </div>

              <!-- 第五列：总量 -->
              <div class="total-volume-col">
                {{ getTotalVolume(item) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ctpService } from '../services/ctpService'
import { OrderRequest } from '../types/ctp'

import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow'
import { useContractStore } from '@/stores/contractStore'
import type { ContractInfo } from '@/types/trading'
import { contractService } from '../services/contractService'
import { message, Modal } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'


// 从URL参数中获取合约代码
const getContractCodeFromUrl = (): string | null => {
  const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '')
  return urlParams.get('contract')
}

interface OrderData {
  price: number
  buyVolume: number
  sellVolume: number
  level: string
}

interface SelectedCell {
  type: 'sell' | 'buy'
  field: 'cancel' | 'buy' | 'sell' | 'price'
  value: number
  data: OrderData
  index: number
  quantity?: number  // 下单数量（可选）
}

const isUsingRealData = ref(false)

// 订单状态管理
const hasActiveOrder = ref(false)
const lastOrderRef = ref('')
const orderStatus = ref('') // 订单状态：submitted, partial, filled, cancelled, rejected
const orderDetails = ref(null) // 订单详细信息

// 合约状态管理
const { currentContract, setCurrentContract } = useContractStore()
const panelContract = ref<ContractInfo | null>(null)

// 窗口ID管理 - 每个TradingPanel窗口的唯一标识
const windowId = ref<string>('')

// 交易相关
const selectedCell = ref<SelectedCell | null>(null)  // 当前选中的单元格（用于下单/撤单）

const lightOrderQuantity = ref(1)                   // 轻仓下单数量（手）- 鼠标左键
const heavyOrderQuantity = ref(20)                  // 重仓下单数量（手）- 鼠标右键
const orderPrice = ref(20)                          // 下单价格（点击价格档位时自动填入）
const orderType = ref('A')                          // 订单类型：A=默认模式, B=特殊模式

// 交易选项配置
const options = ref({
  autoHand: false,                          // 自动手数：是否自动计算下单手数
  cLimit345: false,                         // C限制345：特定的交易限制规则
  cLimit550: false,                         // C限制550：另一种交易限制规则
  noLimit: false,                           // 无限制：取消所有交易限制
  noCombo: false,                           // 无组合：禁用组合交易功能
  upLimit: false                            // 涨停限制：涨停价格限制开关
})

// 持仓信息
const netPosition = ref(0)               // 净持仓（多头-空头的净值）
const cPosition = ref(0)                    // C仓位（可能指Close平仓相关持仓）
const tPosition = ref(0)                    // T仓位（可能指Today今日持仓）
const pnlValue = ref(0)                     // 盈亏值（Profit and Loss）

// 界面缩放
const fontSize = ref(11)                    // 字体大小（像素）
const cellHeight = ref(18)                  // 单元格高度（像素）



// 当前价格和表格控制
const currentPrice = ref(0)           // 当前价格/最新价（等待行情数据）
const tableContainer = ref<HTMLElement>()   // 表格容器引用
const priceDirection = ref<'up' | 'down' | 'neutral'>('neutral')  // 价格变化方向

// 市场数据 - 使用真实行情数据，初始值为0等待行情更新
const priceChangePercent = ref('')           // 价格变化百分比（相对于昨结算价）- 从真实行情计算
const totalVolume = ref(0)                  // 总成交量（手）- 从CTP行情数据获取
const totalPosition = ref(0)                // 总持仓量（手）- 从CTP行情数据获取
const dailyPositionChange = ref(0)          // 日内持仓变化（手）- 从CTP行情数据计算
const redValue = ref(0)                     // 红色数值显示（用于特殊标记）
const blueValue = ref(0)                    // 蓝色数值显示（用于特殊标记）




// 动态生成的卖盘数据（当前价格之上27档）
const sellOrders = ref<OrderData[]>([])

// 动态生成的买盘数据（当前价格之下27档）
const buyOrders = ref<OrderData[]>([])

// 价格档位配置（固定显示档位数）
const PRICE_LEVELS = {
  SELL_LEVELS: 20,  // 卖盘档位数（黑条上方红色区域）
  BUY_LEVELS: 20,   // 买盘档位数（黑条下方蓝色区域）
  PRICE_STEP: 1     // 价格步长
}

// 存储真实行情数据的映射表（价格 -> 买卖量）
const marketDataMap = ref<Map<number, { bidVolume: number, askVolume: number }>>(new Map())

// 数据更新锁，防止并发修改
let isUpdatingOrders = false

// 根据买一价和卖一价动态生成价格档位数据
const generatePriceOrders = (bidPrice1?: number, askPrice1?: number) => {
  if (isUpdatingOrders) {
    console.log('⚠️ 正在更新档位数据，跳过重复生成');
    return;
  }

  isUpdatingOrders = true;
  console.log('🔄 根据买一价和卖一价生成档位数据:', { bidPrice1, askPrice1 });

  try {
    // 生成卖盘数据（从卖一价开始往上递增）
    const newSellOrders: OrderData[] = [];
    if (askPrice1 && askPrice1 > 0) {
      for (let i = 1; i <= PRICE_LEVELS.SELL_LEVELS; i++) {
        const price = askPrice1 + (i - 1) * PRICE_LEVELS.PRICE_STEP;
        const marketData = marketDataMap.value.get(price);

        newSellOrders.push({
          price: price,
          buyVolume: 0,
          sellVolume: marketData?.askVolume || 0,
          level: i.toString()
        });
      }
      // 卖盘按价格从高到低排序（倒序显示）
      newSellOrders.reverse();
    } else {
      // 如果没有卖一价，生成空数据
      for (let i = 1; i <= PRICE_LEVELS.SELL_LEVELS; i++) {
        newSellOrders.push({
          price: 0,
          buyVolume: 0,
          sellVolume: 0,
          level: i.toString()
        });
      }
    }

    // 生成买盘数据（从买一价开始往下递减）
    const newBuyOrders: OrderData[] = [];
    if (bidPrice1 && bidPrice1 > 0) {
      for (let i = 1; i <= PRICE_LEVELS.BUY_LEVELS; i++) {
        const price = bidPrice1 - (i - 1) * PRICE_LEVELS.PRICE_STEP;
        const marketData = marketDataMap.value.get(price);

        newBuyOrders.push({
          price: price,
          buyVolume: marketData?.bidVolume || 0,
          sellVolume: 0,
          level: i.toString()
        });
      }
    } else {
      // 如果没有买一价，生成空数据
      for (let i = 1; i <= PRICE_LEVELS.BUY_LEVELS; i++) {
        newBuyOrders.push({
          price: 0,
          buyVolume: 0,
          sellVolume: 0,
          level: i.toString()
        });
      }
    }

    // 原子性更新数据
    sellOrders.value = newSellOrders;
    buyOrders.value = newBuyOrders;

    console.log('✅ 档位数据生成完成:', {
      卖盘档位: newSellOrders.length,
      买盘档位: newBuyOrders.length,
      买一价: bidPrice1,
      卖一价: askPrice1,
      买盘价格范围: bidPrice1 ? `${newBuyOrders[newBuyOrders.length - 1]?.price} - ${newBuyOrders[0]?.price}` : '无数据',
      卖盘价格范围: askPrice1 ? `${newSellOrders[newSellOrders.length - 1]?.price} - ${newSellOrders[0]?.price}` : '无数据'
    });
  } finally {
    isUpdatingOrders = false;
  }
}

// 生成空的价格档位以铺满表格
const generateEmptyPriceOrders = () => {
  // 生成空的卖盘数据 - 按倒序排列
  const emptySellOrders: OrderData[] = []
  for (let i = 1; i <= PRICE_LEVELS.SELL_LEVELS; i++) {
    emptySellOrders.push({
      price: 0,
      buyVolume: 0,
      sellVolume: 0,
      level: i.toString()
    })
  }
  // 卖盘倒序排列
  emptySellOrders.reverse()

  // 生成空的买盘数据
  const emptyBuyOrders: OrderData[] = []
  for (let i = 1; i <= PRICE_LEVELS.BUY_LEVELS; i++) {
    emptyBuyOrders.push({
      price: 0,
      buyVolume: 0,
      sellVolume: 0,
      level: i.toString()
    })
  }

  sellOrders.value = emptySellOrders
  buyOrders.value = emptyBuyOrders
}

// 缩放功能
const zoomIn = () => {
  fontSize.value = Math.min(fontSize.value + 1, 20)
  cellHeight.value = Math.min(cellHeight.value + 2, 30)
}

const zoomOut = () => {
  fontSize.value = Math.max(fontSize.value - 1, 8)
  cellHeight.value = Math.max(cellHeight.value - 2, 12)
}

// 判断是否选中
const isSelected = (type: 'sell' | 'buy', field: 'cancel' | 'buy' | 'sell' | 'price', index: number) => {
  return selectedCell.value?.type === type &&
         selectedCell.value?.field === field &&
         selectedCell.value?.index === index
}

// 处理撤单点击
const handleCancelClick = (type: 'sell' | 'buy', data: OrderData, index: number) => {
  selectedCell.value = {
    type,
    field: 'cancel',
    value: data.price,
    data,
    index
  }

  console.log('点击撤单:', {
    type: type === 'sell' ? '卖盘' : '买盘',
    price: data.price,
    level: data.level
  })

  // 执行撤单操作
  cancelOrder()
}

// 处理下单点击
const handleOrderClick = (orderType: 'buy' | 'sell', data: OrderData, index: number, event?: MouseEvent) => {
  // 根据鼠标按键决定下单数量
  const isRightClick = event?.button === 2 // 右键
  const orderQuantity = isRightClick ? heavyOrderQuantity.value : lightOrderQuantity.value
  const orderMode = isRightClick ? '重仓' : '轻仓'

  selectedCell.value = {
    type: orderType,
    field: orderType,
    value: data.price,
    data,
    index,
    quantity: orderQuantity // 添加数量信息
  }

  // 自动填入价格
  orderPrice.value = data.price

  console.log('点击下单:', {
    orderType: orderType === 'buy' ? '买单' : '卖单',
    price: data.price,
    level: data.level,
    mode: orderType,
    quantity: orderQuantity,
    orderMode: orderMode,
    mouseButton: isRightClick ? '右键' : '左键'
  })

  // 执行下单操作
  placeOrder()
}

// 下单操作
const placeOrder = async () => {
  if (!selectedCell.value) return

  const { type, quantity } = selectedCell.value

  // 使用selectedCell中的数量，如果没有则使用轻仓数量作为默认值
  const orderQuantity = quantity || lightOrderQuantity.value

  try {
    // 检查结算确认状态  暂时先保留
    const settlementResult = await ctpService.checkSettlementConfirmStatus()
    if (!settlementResult.success || !settlementResult.data) {
      console.warn('❌ 结算结果未确认，无法下单')
      console.log('🔄 尝试自动进行结算确认...')

      // 尝试自动结算确认
      const confirmResult = await ctpService.settlementInfoConfirm()
      if (!confirmResult.success) {
        console.error('❌ 自动结算确认失败:', confirmResult.error)
        return
      }
      console.log('✅ 自动结算确认成功')
    }

    // 获取当前合约代码
    const contractCode = currentContract.value?.code || panelContract.value?.code

    if (!contractCode) {
      console.warn('请先选择合约')
      return
    }

    // 构建订单请求
    const orderRequest: OrderRequest = {
      instrument_id: contractCode, // 使用当前选中的合约
      direction: type === 'buy' ? '0' : '1', // 0=买入, 1=卖出
      price: orderPrice.value,
      volume: orderQuantity,
      order_type: '2', // 2=限价单 (1=市价单)
      offset_flag: '0', // 0=开仓
      hedge_flag: '1', // 1=投机
      time_condition: '3', // 3=当日有效
      volume_condition: '1' // 1=任何数量
    }

    console.log('📤 发送下单请求:', orderRequest)

    // 发送下单请求
    const result = await ctpService.insertOrder(orderRequest)

    if (result.success) {
      const orderMode = quantity === heavyOrderQuantity.value ? '重仓' : '轻仓'
      console.log(`✅ ${orderMode}下单成功: ${type === 'buy' ? '买入' : '卖出'} ${orderQuantity}手 @${orderPrice.value}`, result.data)

      // 清除选择
      clearSelection()
    } else {
      console.error(`❌ 下单失败: ${result.error}`)
    }

  } catch (error) {
    console.error('❌ 下单异常:', error)
  }
}

// 撤单操作
const cancelOrder = async () => {
  if (!selectedCell.value) return

  const { type, data } = selectedCell.value

  try {

    // 注意：这里需要有实际的订单引用号才能撤单
    // 在真实场景中，应该从订单列表中获取要撤销的订单引用号
    const orderRef = `order_${Date.now()}` // 临时的订单引用号，实际应该从订单管理中获取

    console.log('📤 发送撤单请求:', { orderRef, type, level: data.level })

    // 发送撤单请求
    const result = await ctpService.cancelOrder(orderRef)

    if (result.success) {
      console.log(`✅ 撤单成功: ${type === 'sell' ? '卖盘' : '买盘'} 档位${data.level}`, result.data)

      // 清除选择
      clearSelection()
    } else {
      console.error(`❌ 撤单失败: ${result.error}`)
    }

  } catch (error) {
    console.error('❌ 撤单异常:', error)
  }
}

// 清除选择
const clearSelection = () => {
  selectedCell.value = null
}

// 获取总量显示
const getTotalVolume = (item: OrderData) => {
  // 显示买量和卖量的总和
  const buyVol = item.buyVolume || 0
  const sellVol = item.sellVolume || 0
  const total = buyVol + sellVol
  return total > 0 ? '' : ''
}

// 实时更新当前价格（固定布局，无需滚动）
const updateCurrentPrice = (newPrice: number) => {
  const oldPrice = currentPrice.value
  const newPriceRounded = Math.round(newPrice)

  // 更新价格方向
  if (newPriceRounded > oldPrice) {
    priceDirection.value = 'up'
  } else if (newPriceRounded < oldPrice) {
    priceDirection.value = 'down'
  } else {
    priceDirection.value = 'neutral'
  }

  currentPrice.value = newPriceRounded

  // 3秒后重置方向为中性
  setTimeout(() => {
    priceDirection.value = 'neutral'
  }, 3000)
}

// 行情数据监听器引用，用于在组件卸载时清理
let marketDataListener: ((data: any) => void) | null = null

// 初始化窗口ID
const initializeWindowId = () => {
  const currentWindow = getCurrentWebviewWindow()
  windowId.value = currentWindow.label
  console.log(`🪟 [WINDOW] 初始化窗口ID: ${windowId.value}`)
}

// 当前订阅的合约代码列表
const subscribedContracts = ref<string[]>([])

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {

  switch (event.key) {
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case 'Escape':
      event.preventDefault()
      clearSelection()
      break
    case 'Enter':
      if (selectedCell.value) {
        event.preventDefault()
        placeOrder()
      }
      break
    case 'Delete':
    case 'Backspace':
      if (selectedCell.value) {
        event.preventDefault()
        cancelOrder()
      }
      break

  }
}

// 初始化合约数据和行情
const initMarketData = async () => {
  try {
    console.log('🔍 开始初始化交易面板...')

    // 初始化合约信息
    await initializeContractInfo()

    // 设置为使用真实数据
    console.log('📊 使用CTP真实数据')
    isUsingRealData.value = true

  } catch (error) {
    console.error('❌ 初始化交易面板失败:', error)
  }
}



// 初始化合约信息
const initializeContractInfo = async () => {
  try {
    // 首先检查URL参数中是否有合约代码
    const contractCodeFromUrl = getContractCodeFromUrl()

    if (contractCodeFromUrl) {
      console.log('从URL参数获取合约代码:', contractCodeFromUrl)

      // 根据合约代码查询合约信息
      try {
        const contractInfo = await contractService.getContractByCode(contractCodeFromUrl)
        if (contractInfo) {
          console.log('找到合约信息:', contractInfo)
          panelContract.value = contractInfo
          setCurrentContract(contractInfo)

          // 订阅行情数据
          await queryMarketData(contractCodeFromUrl)
          return
        }
      } catch (error) {
        console.warn('查询合约信息失败:', error)
      }

      // 如果查询合约信息失败，直接使用合约代码订阅行情
      console.log('直接使用合约代码订阅行情:', contractCodeFromUrl)
      await queryMarketData(contractCodeFromUrl)
      return
    }

    // 检查是否有当前选中的合约
    if (currentContract.value) {
      console.log('使用当前选中的合约:', currentContract.value)
      panelContract.value = currentContract.value

      // 订阅选中合约的行情数据
      await queryMarketData(currentContract.value.code)
    } else {
      console.log('未找到选中的合约，使用默认测试合约')

      // 设置默认测试合约 - 螺纹钢主力合约
      const defaultContract = {
        code: 'rb2509',
        name: '螺纹钢2509',
        fullCode: 'rb2509',
        category: '黑色金属',
        exchange: 'SHFE',
        lastPrice: 3520,
        changePercent: 0.85,
        volume: 125680,
        openInterest: 89456,
        high: 3545,
        low: 3498,
        open: 3510,
        preClose: 3490,
        bid1: 3519,
        ask1: 3521,
        bidVolume1: 50,
        askVolume1: 30,
        multiplier: 10, // 合约乘数
        tickSize: 1, // 最小变动价位
        marginRate: 0.08 // 保证金率
      }

      currentContract.value = defaultContract
      panelContract.value = defaultContract
      setCurrentContract(defaultContract)

      console.log('✅ 已设置默认测试合约:', defaultContract)

      // 订阅默认合约的行情数据
      await queryMarketData(defaultContract.code)
    }
  } catch (error) {
    console.error('❌ 初始化合约信息失败:', error)
  }
}

// 检查结算确认状态
const checkSettlementStatus = async () => {
  try {
    console.log('🔍 检查结算确认状态...')

    const result = await ctpService.checkSettlementConfirmStatus()

    if (result.success) {
      if (result.data) {
        console.log('✅ 结算已确认，可以进行交易')
      } else {
        console.log('⚠️ 结算未确认，需要进行结算确认')

        // 自动进行结算确认
        await performSettlementConfirm()
      }
    } else {
      console.log('❌ 检查结算确认状态失败:', result.error)
    }
  } catch (error) {
    console.error('❌ 检查结算确认状态异常:', error)
  }
}

// 执行结算确认
const performSettlementConfirm = async () => {
  try {
    console.log('📋 开始结算确认...')

    const result = await ctpService.settlementInfoConfirm()

    if (result.success) {
      console.log('✅ 结算确认成功，现在可以进行交易')
    } else {
      console.log('❌ 结算确认失败:', result.error)

      // 如果结算确认失败，可以提示用户手动处理
      if (result.error?.includes('结算结果未确认')) {
        console.log('⚠️ 请联系期货公司确认结算结果或稍后重试')
      }
    }
  } catch (error) {
    console.error('❌ 结算确认异常:', error)
  }
}

// 专门用于窗口关闭时的强制清理函数
const forceCleanupOnWindowClose = async (): Promise<void> => {
  console.log(`🚨 窗口 ${windowId.value} 关闭强制清理开始...`)

  const contractsToCleanup = [...subscribedContracts.value]

  // 立即移除事件监听器，防止继续接收数据（窗口级别）
  if (marketDataListener && windowId.value) {
    ctpService.offWindow('market_data', windowId.value, marketDataListener)
    marketDataListener = null
    console.log(`✅ 强制移除窗口 ${windowId.value} 的行情数据监听器`)
  }

  // 清理该窗口的所有资源
  if (windowId.value) {
    ctpService.cleanupWindow(windowId.value)
  }

  // 如果有订阅的合约，尝试取消订阅（带短超时）
  if (subscribedContracts.value.length > 0) {
    try {
      console.log('🔄 强制取消订阅合约行情:', subscribedContracts.value)

      // 使用更短的超时时间（1.5秒）
      const unsubscribePromise = ctpService.unsubscribeMarketData(subscribedContracts.value)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('窗口关闭超时')), 1500)
      })

      const result = await Promise.race([unsubscribePromise, timeoutPromise]) as any

      if (result.success) {
        console.log(`✅ 窗口关闭：已断开 ${contractsToCleanup.join(', ')} 行情连接`)
      } else {
        console.warn(`⚠️ 窗口关闭：断开行情连接失败 - ${result.error}`)
      }
    } catch (error) {
      console.error('❌ 窗口关闭前取消订阅异常:', error)
      const errorMsg = error instanceof Error ? error.message : '未知错误'

      if (errorMsg.includes('超时')) {
        console.warn('⚠️ 窗口关闭超时，强制清理本地状态')
      } else {
        console.error(`❌ 窗口关闭清理异常: ${errorMsg}`)
      }
    }

    // 强制清空订阅列表
    subscribedContracts.value = []
  }

  console.log('✅ 窗口关闭强制清理完成')
}

// 清理当前行情订阅
const cleanupCurrentSubscription = async (showUserMessage: boolean = false) => {
  const contractsToCleanup = [...subscribedContracts.value] // 创建副本用于日志

  console.log('🧹 开始清理行情订阅:', {
    窗口ID: windowId.value,
    监听器存在: !!marketDataListener,
    订阅合约数量: subscribedContracts.value.length,
    订阅合约列表: contractsToCleanup,
    显示用户消息: showUserMessage
  })

  // 移除现有的行情数据监听器（窗口级别）
  if (marketDataListener && windowId.value) {
    ctpService.offWindow('market_data', windowId.value, marketDataListener)
    marketDataListener = null
    console.log(`✅ 已移除窗口 ${windowId.value} 的行情数据监听器`)
  }

  // 取消订阅现有合约
  if (subscribedContracts.value.length > 0) {
    try {
      console.log('🔄 正在取消订阅合约行情:', subscribedContracts.value)

      // 创建一个带超时的Promise来防止无限等待
      const unsubscribePromise = ctpService.unsubscribeMarketData(subscribedContracts.value)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('取消订阅超时')), 3000) // 3秒超时
      })

      const result = await Promise.race([unsubscribePromise, timeoutPromise]) as any
      if (result.success) {
        console.log('✅ 成功取消订阅所有合约行情:', contractsToCleanup)

        if (showUserMessage) {
          console.log(`✅ 已断开 ${contractsToCleanup.join(', ')} 行情数据连接`)
        }
      } else {
        console.warn(`⚠️ 取消订阅合约失败: ${result.error}`)

        if (showUserMessage) {
          console.warn(`⚠️ 断开行情连接失败`)
        }
      }
    } catch (error) {
      console.error('❌ 取消订阅合约异常:', error)

      if (showUserMessage) {
        const errorMsg = error instanceof Error ? error.message : '未知错误'
        if (errorMsg.includes('超时')) {
          console.warn('⚠️ 由于超时，强制清理本地订阅状态')
        }
      }
    }

    // 无论成功失败都清空已订阅列表，避免状态不一致
    subscribedContracts.value = []
  }
}

// 根据合约代码查询行情数据
const queryMarketData = async (contractCode: string): Promise<boolean> => {
  try {
    console.log(`🔍 查询行情数据: ${contractCode}`)

    // 先清理现有订阅
    await cleanupCurrentSubscription()

    // 订阅新的行情数据
    const subscribeResult = await ctpService.subscribeMarketData([contractCode])

    if (subscribeResult.success) {
      // 添加到已订阅列表
      if (!subscribedContracts.value.includes(contractCode)) {
        subscribedContracts.value.push(contractCode)
      }

      // 创建行情数据监听器（窗口级别）
      marketDataListener = (data: any) => {
        if (data.instrument_id === contractCode) {
          updateMarketData(data)
        }
      }

      // 监听行情数据更新（窗口级别）
      ctpService.onWindow('market_data', windowId.value, marketDataListener)

      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

// 更新行情数据
const updateMarketData = (data: any) => {
  try {
    console.log(`📊 [${windowId.value}] 更新行情数据:`, data)

    // 更新当前价格
    if (data.last_price && data.last_price > 0) {
      const newPrice = Math.round(data.last_price)
      if (newPrice !== currentPrice.value) {
        updateCurrentPrice(newPrice)
      }
    }

    // 更新成交量（真实数据）
    if (data.volume && data.volume > 0) {
      totalVolume.value = data.volume
      console.log('📊 更新成交量:', data.volume)
    }

    // 更新持仓量（真实数据）
    if (data.open_interest && data.open_interest > 0) {
      totalPosition.value = data.open_interest
      console.log('📊 更新持仓量:', data.open_interest)
    }

    // 计算日内持仓变化（真实数据）
    if (data.open_interest && data.pre_open_interest) {
      const positionChange = data.open_interest - data.pre_open_interest
      dailyPositionChange.value = positionChange
      console.log('📊 更新日内持仓变化:', positionChange)
    }

    // 计算价格变化百分比（真实数据）
    if (data.last_price && data.pre_settlement_price && data.pre_settlement_price > 0) {
      const changePercent = ((data.last_price - data.pre_settlement_price) / data.pre_settlement_price) * 100
      priceChangePercent.value = changePercent.toFixed(2)
      console.log('📊 更新价格变化百分比:', changePercent.toFixed(2) + '%')
    }

    // 记录买一价和卖一价用于生成档位
    let bidPrice1: number | undefined
    let askPrice1: number | undefined

    // 更新买卖盘数据到marketDataMap
    if (data.bid_price1 && data.bid_volume1) {
      bidPrice1 = Math.round(data.bid_price1)
      const existingData = marketDataMap.value.get(bidPrice1) || { bidVolume: 0, askVolume: 0 }
      marketDataMap.value.set(bidPrice1, {
        bidVolume: data.bid_volume1,
        askVolume: existingData.askVolume
      })
      console.log('📊 更新买一盘:', `价格=${bidPrice1}, 量=${data.bid_volume1}`)
    }

    if (data.ask_price1 && data.ask_volume1) {
      askPrice1 = Math.round(data.ask_price1)
      const existingData = marketDataMap.value.get(askPrice1) || { bidVolume: 0, askVolume: 0 }
      marketDataMap.value.set(askPrice1, {
        bidVolume: existingData.bidVolume,
        askVolume: data.ask_volume1
      })
      console.log('📊 更新卖一盘:', `价格=${askPrice1}, 量=${data.ask_volume1}`)
    }

    // 如果有买一价或卖一价数据，重新生成价格档位
    if (bidPrice1 || askPrice1) {
      generatePriceOrders(bidPrice1, askPrice1)
    }

    // 更新其他价格信息
    if (data.open_price) {
      console.log('📊 开盘价:', data.open_price)
    }
    if (data.highest_price) {
      console.log('📊 最高价:', data.highest_price)
    }
    if (data.lowest_price) {
      console.log('📊 最低价:', data.lowest_price)
    }

  } catch (error) {
    console.error('❌ 更新行情数据失败:', error)
  }
}



// 监听合约变化，自动切换行情订阅
watch(
  () => currentContract.value,
  async (newContract, oldContract) => {
    if (newContract && newContract.code !== oldContract?.code) {
      console.log('🔄 检测到合约变化:', {
        从: oldContract?.code || '无',
        到: newContract.code
      })

      // 更新面板合约信息
      panelContract.value = newContract

      // 重新订阅新合约的行情数据
      const subscribeSuccess = await queryMarketData(newContract.code)
      if (subscribeSuccess) {
        console.log(`✅ 已切换到 ${newContract.name} (${newContract.code}) 行情`)
      } else {
        console.warn(`⚠️ 切换到 ${newContract.name} 行情失败`)
      }
    }
  },
  { immediate: false } // 不立即执行，避免初始化时重复订阅
)

// 组件挂载和卸载时的事件监听
onMounted(async () => {
  // 首先初始化窗口ID
  initializeWindowId()

  document.addEventListener('keydown', handleKeydown)

  // 初始化合约信息
  initializeContractInfo()

  // 生成空的价格档位以铺满表格
  generateEmptyPriceOrders()

  // 初始化行情数据
  initMarketData()

  // 检查结算确认状态
  await checkSettlementStatus()

  // 固定布局，无需滚动

  console.log(`交易面板已挂载，窗口ID: ${windowId.value}`)

  // 监听窗口关闭事件，确保在用户通过窗口控制按钮关闭时也能清理资源
  const currentWindow = getCurrentWebviewWindow()
  await currentWindow.onCloseRequested(async (event) => {
    console.log('🔄 检测到窗口关闭请求，阻止关闭并开始清理行情数据...')

    // 阻止窗口立即关闭
    event.preventDefault()

    try {
      // 使用专门的窗口关闭清理函数
      console.log('⏳ 正在等待行情数据清理完成...')
      await forceCleanupOnWindowClose()

      // 清理完成后，手动关闭窗口
      console.log('🔄 行情数据清理完成，现在关闭窗口...')
      await currentWindow.close()

    } catch (error) {
      console.error('❌ 窗口关闭前清理行情数据失败，强制关闭窗口:', error)

      // 即使清理失败，也要关闭窗口
      setTimeout(async () => {
        await currentWindow.close()
      }, 500) // 给用户500ms时间看到错误消息
    }
  })
})

// 获取显示的合约信息
const getDisplayContract = () => {
  return panelContract.value || currentContract.value
}

// 格式化涨跌幅
const formatChangePercent = (percent) => {
  if (percent === undefined || percent === null) return '--'
  const sign = percent >= 0 ? '+' : ''
  return `${sign}${percent.toFixed(2)}%`
}

// 获取涨跌幅样式类
const getChangeClass = (percent) => {
  if (percent === undefined || percent === null) return 'neutral'
  if (percent > 0) return 'positive'
  if (percent < 0) return 'negative'
  return 'neutral'
}

// 格式化成交量
const formatVolume = (volume) => {
  if (!volume) return '--'
  if (volume >= 10000) {
    return `${(volume / 10000).toFixed(1)}万`
  }
  return volume.toLocaleString()
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    'submitted': '已提交',
    'partial': '部分成交',
    'filled': '全部成交',
    'cancelled': '已撤销',
    'rejected': '已拒绝',
    'timeout': '可能超时',
    'rejecting_cancel': '撤单被拒',
    'unknown': '状态未知'
  }
  return statusMap[status] || status || '未知'
}

// 智能价格选择策略
const getOptimalOrderPrice = (contractInfo: any, contractCode: string) => {
  const bid1Price = contractInfo?.bid1
  const ask1Price = contractInfo?.ask1
  const lastPrice = contractInfo?.lastPrice
  const bid1Volume = contractInfo?.bidVolume1 || 0
  const ask1Volume = contractInfo?.askVolume1 || 0

  console.log('📊 分析最优下单价格:')
  console.log(`   买一: ${bid1Price} (${bid1Volume}手)`)
  console.log(`   卖一: ${ask1Price} (${ask1Volume}手)`)
  console.log(`   最新价: ${lastPrice}`)

  // 策略1: 如果买一价存在且合理，使用买一价（最保守策略）
  if (bid1Price && bid1Price > 0 && bid1Volume > 0) {
    return {
      price: bid1Price,
      strategy: `买一价排队 (${bid1Volume}手在前)`,
      confidence: 'high'
    }
  }

  // 策略2: 如果最新价存在，使用最新价-1（稍微激进）
  if (lastPrice && lastPrice > 0) {
    const adjustedPrice = Math.max(lastPrice - 1, 1)
    return {
      price: adjustedPrice,
      strategy: `最新价-1 (${lastPrice}-1=${adjustedPrice})`,
      confidence: 'medium'
    }
  }

  // 策略3: 如果卖一价存在，使用卖一价-2（更激进）
  if (ask1Price && ask1Price > 2) {
    const adjustedPrice = ask1Price - 2
    return {
      price: adjustedPrice,
      strategy: `卖一价-2 (${ask1Price}-2=${adjustedPrice})`,
      confidence: 'medium'
    }
  }

  // 策略4: 使用合约默认价格（兜底方案）
  let defaultPrice
  if (contractCode.startsWith('a')) {
    defaultPrice = 4100 // 豆一合约
  } else if (contractCode.startsWith('rb')) {
    defaultPrice = 3500 // 螺纹钢合约
  } else if (contractCode.startsWith('i')) {
    defaultPrice = 800  // 铁矿石合约
  } else if (contractCode.startsWith('j')) {
    defaultPrice = 2000 // 焦炭合约
  } else {
    defaultPrice = 3000 // 通用默认价格
  }

  return {
    price: defaultPrice,
    strategy: `默认价格 (${contractCode}=${defaultPrice})`,
    confidence: 'low'
  }
}

// 验证价格是否在合理范围内
const validateOrderPrice = (price: number, contractCode: string) => {
  if (price <= 0) {
    return { valid: false, reason: '价格必须大于0' }
  }

  if (price > 100000) {
    return { valid: false, reason: '价格过高，可能超出涨跌停板' }
  }

  // 根据合约类型检查价格合理性
  if (contractCode.startsWith('a') && (price < 3000 || price > 6000)) {
    return { valid: false, reason: '豆一价格通常在3000-6000范围内' }
  }

  if (contractCode.startsWith('rb') && (price < 2000 || price > 5000)) {
    return { valid: false, reason: '螺纹钢价格通常在2000-5000范围内' }
  }

  return { valid: true, reason: '价格验证通过' }
}

// 下单前综合检查
const preOrderCheck = async (sessionId: string, contractCode: string, orderPrice: number) => {
  const checks = []

  // 1. 检查交易时间
  const now = new Date()
  const hour = now.getHours()
  const minute = now.getMinutes()
  const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`

  // 期货交易时间检查（简化版）
  const isInTradingTime = (
    (hour >= 9 && hour < 11) ||  // 上午9:00-10:15, 10:30-11:30
    (hour >= 13 && hour < 15) || // 下午13:30-15:00
    (hour >= 21 && hour < 23)    // 夜盘21:00-23:00（部分品种）
  )

  if (!isInTradingTime) {
    checks.push({
      type: 'warning',
      message: `当前时间 ${timeStr} 可能不在交易时间内`,
      suggestion: '请确认该合约的交易时间'
    })
  } else {
    checks.push({
      type: 'success',
      message: `交易时间检查通过 (${timeStr})`,
      suggestion: ''
    })
  }

  // 2. 检查合约代码格式
  if (contractCode.length < 4 || contractCode.length > 8) {
    checks.push({
      type: 'error',
      message: '合约代码格式可能不正确',
      suggestion: '请检查合约代码是否正确，如 a2509, rb2509'
    })
  } else {
    checks.push({
      type: 'success',
      message: '合约代码格式检查通过',
      suggestion: ''
    })
  }

  // 3. 检查价格合理性
  const priceValidation = validateOrderPrice(orderPrice, contractCode)
  if (!priceValidation.valid) {
    checks.push({
      type: 'error',
      message: priceValidation.reason,
      suggestion: '请调整下单价格'
    })
  } else {
    checks.push({
      type: 'success',
      message: priceValidation.reason,
      suggestion: ''
    })
  }

  // 4. 检查交易状态
  try {
    const statusResult = await invoke('check_trading_status', { sessionId }) as any
    if (statusResult.success && statusResult.data?.ready_for_trading) {
      checks.push({
        type: 'success',
        message: '交易环境就绪',
        suggestion: ''
      })
    } else {
      const issues = []
      if (!statusResult.data?.trader_connected) issues.push('交易API未连接')
      if (!statusResult.data?.login_status) issues.push('未登录')
      if (!statusResult.data?.settlement_confirmed) issues.push('未确认结算')

      checks.push({
        type: 'error',
        message: `交易环境未就绪: ${issues.join(', ')}`,
        suggestion: '请先完成登录和结算确认'
      })
    }
  } catch (error) {
    checks.push({
      type: 'warning',
      message: '无法检查交易状态',
      suggestion: '请手动确认交易环境'
    })
  }

  return {
    passed: checks.every(check => check.type !== 'error'),
    checks,
    summary: {
      total: checks.length,
      success: checks.filter(c => c.type === 'success').length,
      warning: checks.filter(c => c.type === 'warning').length,
      error: checks.filter(c => c.type === 'error').length
    }
  }
}

// 智能下单函数 - 确保下单成功
const placeTestOrder = async () => {
  try {
    console.log('📋 开始智能下单...')
    message.loading('正在分析市场并提交订单...', 0)

    // 1. 获取活跃的trader session
    const activeSessionResult = await invoke('get_active_trader_session') as any
    let traderSessionId = ''
    if (activeSessionResult.success) {
      traderSessionId = activeSessionResult.data
      console.log('✅ 找到活跃的trader session:', traderSessionId)
    } else {
      message.destroy()
      message.error('未找到活跃的交易会话，请先登录')
      return
    }

    // 2. 检查交易状态
    console.log('🔍 检查交易状态...')
    const statusResult = await invoke('check_trading_status', { sessionId: traderSessionId }) as any
    if (!statusResult.success || !statusResult.data?.ready_for_trading) {
      message.destroy()
      console.error('❌ 交易状态检查失败:', statusResult.data)

      // 分析具体问题
      const issues = []
      if (!statusResult.data?.trader_connected) issues.push('交易API未连接')
      if (!statusResult.data?.login_status) issues.push('未登录')
      if (!statusResult.data?.settlement_confirmed) issues.push('未确认结算')

      message.error(`交易环境未就绪: ${issues.join(', ')}`)
      return
    }
    console.log('✅ 交易状态检查通过')

    // 3. 获取当前合约信息和市场数据
    const currentContractInfo = getDisplayContract() as any
    const contractCode = currentContractInfo?.code || 'a2509'

    // 获取实时行情数据
    const bid1Price = currentContractInfo?.bid1
    const ask1Price = currentContractInfo?.ask1
    const lastPrice = currentContractInfo?.lastPrice
    const bid1Volume = currentContractInfo?.bidVolume1 || 0
    const ask1Volume = currentContractInfo?.askVolume1 || 0

    console.log('� 当前市场数据:')
    console.log(`   合约: ${contractCode}`)
    console.log(`   最新价: ${lastPrice}`)
    console.log(`   买一: ${bid1Price} (${bid1Volume}手)`)
    console.log(`   卖一: ${ask1Price} (${ask1Volume}手)`)

    // 4. 使用智能价格选择策略
    const priceResult = getOptimalOrderPrice(currentContractInfo, contractCode)
    const orderPrice: number = priceResult.price
    const priceStrategy = priceResult.strategy

    console.log(`📋 选择价格策略: ${priceStrategy}`)
    console.log(`📋 下单价格: ${orderPrice}`)
    console.log(`📋 策略可信度: ${priceResult.confidence}`)

    // 5. 下单前综合检查
    console.log('🔍 执行下单前综合检查...')
    const preCheckResult = await preOrderCheck(traderSessionId, contractCode, orderPrice)

    console.log('📋 检查结果:', preCheckResult.summary)
    preCheckResult.checks.forEach(check => {
      if (check.type === 'success') {
        console.log(`✅ ${check.message}`)
      } else if (check.type === 'warning') {
        console.log(`⚠️ ${check.message}`)
      } else if (check.type === 'error') {
        console.log(`❌ ${check.message}`)
      }
    })

    if (!preCheckResult.passed) {
      message.destroy()
      const errorChecks = preCheckResult.checks.filter(c => c.type === 'error')
      const errorMessages = errorChecks.map(c => c.message).join('\n')
      const suggestions = errorChecks.map(c => c.suggestion).filter(s => s).join('\n')

      Modal.error({
        title: '下单前检查失败',
        content: `
          ❌ 发现以下问题:
          ${errorMessages}

          💡 建议解决方案:
          ${suggestions}
        `,
        width: 500,
        okText: '确定'
      })
      return
    }
    console.log('✅ 下单前检查全部通过')

    // 6. 构造优化的订单请求
    const orderRequest = {
      instrument_id: contractCode,
      direction: "0", // 买入
      price: orderPrice,
      volume: 1, // 小量测试
      order_type: "2", // 限价单
      offset_flag: "0", // 开仓
      hedge_flag: "1", // 投机
      time_condition: "3", // 当日有效
      volume_condition: "1" // 任何数量
    }

    console.log('📤 订单详情:', orderRequest)

    // 7. 预检查订单参数
    const diagnosisResult = await invoke('diagnose_order_fields', {
      sessionId: traderSessionId,
      order: orderRequest
    }) as any

    if (diagnosisResult.success && diagnosisResult.data?.field_validation?.valid === false) {
      message.destroy()
      message.error(`订单参数错误: ${diagnosisResult.data.field_validation.message}`)
      console.error('❌ 订单参数诊断失败:', diagnosisResult.data)
      return
    }
    console.log('✅ 订单参数验证通过')

    // 8. 提交订单
    console.log('📤 提交订单到交易所...')
    const result = await invoke('insert_order', {
      sessionId: traderSessionId,
      order: orderRequest
    }) as any

    message.destroy() // 清除loading消息

    if (result.success) {
      message.success('订单提交成功！')
      console.log('✅ 下单成功:', result.data)

      // 更新订单状态
      hasActiveOrder.value = true
      orderStatus.value = 'submitted'

      // 提取订单引用
      const orderRefMatch = result.data.match(/订单引用:\s*(\d+)/)
      if (orderRefMatch) {
        lastOrderRef.value = orderRefMatch[1]
        console.log('📋 订单引用:', lastOrderRef.value)

        // 保存订单详细信息
        orderDetails.value = {
          orderRef: lastOrderRef.value,
          instrumentId: contractCode,
          direction: '买入',
          price: orderPrice,
          volume: 1,
          submitTime: new Date().toLocaleTimeString(),
          status: 'submitted',
          strategy: priceStrategy
        }
      }

      // 设置订单监控
      setTimeout(() => {
        if (orderStatus.value === 'submitted') {
          console.log('⚠️ 订单可能需要更多时间处理')
          orderStatus.value = 'timeout'
        }
      }, 30000)

      // 显示成功信息
      Modal.success({
        title: '🎉 下单成功',
        content: `
          ✅ 订单已成功提交到交易所

          📋 订单详情:
          • 合约代码: ${contractCode}
          • 交易方向: 买入开仓
          • 下单价格: ${orderPrice}
          • 下单数量: 1手
          • 订单引用: ${lastOrderRef.value}
          • 提交时间: ${new Date().toLocaleTimeString()}

          📊 当前市场状况:
          • 最新价: ${currentContractInfo?.lastPrice || '--'}
          • 买一价: ${currentContractInfo?.bid1 || '--'} (${currentContractInfo?.bidVolume1 || 0}手)
          • 卖一价: ${currentContractInfo?.ask1 || '--'} (${currentContractInfo?.askVolume1 || 0}手)

          💡 价格策略: ${priceStrategy}
          🎯 策略可信度: ${priceResult.confidence}

          📈 下一步:
          • 订单状态将在左侧面板实时更新
          • 如需撤单，请点击"撤单"按钮
          • 建议关注市场变化调整策略
        `,
        width: 600,
        okText: '确定'
      })
    } else {
      console.error('❌ 下单失败:', result.error)

      // 分析失败原因并提供建议
      const analysisResult = await invoke('analyze_order_rejection', {
        sessionId: traderSessionId,
        order: orderRequest
      }) as any

      let errorDetails = result.error
      if (analysisResult.success && analysisResult.data?.suggestions) {
        errorDetails += '\n\n建议:\n' + analysisResult.data.suggestions.join('\n')
      }

      message.error('下单失败，请查看详情')
      Modal.error({
        title: '下单失败',
        content: `
          ❌ 订单被拒绝

          📋 错误信息:
          ${result.error}

          📊 订单详情:
          • 合约: ${contractCode}
          • 价格: ${orderPrice}
          • 数量: 1手

          ${analysisResult.success ? `
          💡 可能原因:
          ${analysisResult.data?.issues?.join('\n') || '未知'}

          🔧 建议解决方案:
          ${analysisResult.data?.suggestions?.join('\n') || '请检查交易环境'}
          ` : ''}
        `,
        width: 600,
        okText: '确定'
      })
    }
  } catch (error) {
    message.destroy()
    message.error('下单过程中发生系统错误')
    console.error('❌ 下单异常:', error)

    Modal.error({
      title: '系统错误',
      content: `下单过程中发生异常: ${error.message || error}`,
      okText: '确定'
    })
  }
}

// 测试撤单函数
const cancelTestOrder = async () => {
  try {
    if (!hasActiveOrder.value || !lastOrderRef.value) {
      message.warning('没有可撤销的订单')
      return
    }

    // 检查订单状态
    if (orderStatus.value === 'filled') {
      message.warning('订单已成交，无法撤销')
      return
    }

    if (orderStatus.value === 'cancelled') {
      message.warning('订单已撤销')
      return
    }

    if (orderStatus.value === 'rejected') {
      message.warning('订单已被拒绝，无需撤销')
      hasActiveOrder.value = false
      return
    }

    console.log('🗑️ 开始撤单...')
    console.log('🗑️ 订单状态:', orderStatus.value)
    console.log('🗑️ 订单详情:', orderDetails.value)
    message.loading('正在撤销订单...', 0)

    // 获取活跃的trader session
    const activeSessionResult = await invoke('get_active_trader_session') as any

    let traderSessionId = ''
    if (activeSessionResult.success) {
      traderSessionId = activeSessionResult.data
      console.log('✅ 找到活跃的trader session:', traderSessionId)
    } else {
      message.destroy()
      message.error('未找到活跃的交易会话，请先登录')
      return
    }

    // 获取当前合约信息
    const currentContractInfo = getDisplayContract() as any
    const contractCode = currentContractInfo?.code || 'rb2509'

    console.log('🗑️ 撤销订单:', contractCode, '订单引用:', lastOrderRef.value)

    // 构造撤单请求
    const cancelRequest = {
      instrument_id: contractCode,
      order_ref: lastOrderRef.value,
      front_id: null,
      session_id: null,
      exchange_id: "SHFE", // 上海期货交易所
      order_sys_id: null
    }

    // 调用撤单接口
    const result = await invoke('cancel_order', {
      sessionId: traderSessionId,
      cancelRequest: cancelRequest
    }) as any

    message.destroy() // 清除loading消息

    if (result.success) {
      message.success('撤单请求提交成功')
      console.log('✅ 撤单成功:', result.data)

      // 更新订单状态
      hasActiveOrder.value = false
      lastOrderRef.value = ''
      orderStatus.value = 'cancelled'

      // 更新订单详情
      if (orderDetails.value) {
        orderDetails.value.status = 'cancelled'
        orderDetails.value.cancelTime = new Date().toLocaleTimeString()
      }

      // 显示撤单详情
      Modal.info({
        title: '撤单成功',
        content: `${result.data}\n\n合约: ${contractCode}\n订单引用: ${cancelRequest.order_ref}\n交易所: ${cancelRequest.exchange_id}`,
        width: 500,
        okText: '确定'
      })
    } else {
      const errorMsg = result.error || '未知错误'
      console.error('❌ 撤单失败:', errorMsg)

      // 根据错误类型给出不同的处理建议
      if (errorMsg.includes('撤单找不到相应报单')) {
        message.warning('撤单失败：订单可能已成交或已被撤销')

        // 订单可能已经不存在，清理状态
        hasActiveOrder.value = false
        lastOrderRef.value = ''
        orderStatus.value = 'unknown'

        Modal.warning({
          title: '撤单失败',
          content: `${errorMsg}\n\n可能原因：\n1. 订单已成交\n2. 订单已被撤销\n3. 订单已过期\n4. 系统延迟\n\n建议：请检查订单状态或重新查询持仓`,
          width: 500,
          okText: '确定'
        })
      } else if (errorMsg.includes('撤单被拒绝')) {
        message.error('撤单被拒绝：订单可能正在成交中')
        orderStatus.value = 'rejecting_cancel'
      } else {
        message.error(`撤单失败: ${errorMsg}`)
      }
    }
  } catch (error) {
    message.destroy()
    message.error('撤单过程中发生错误')
    console.error('❌ 撤单时发生错误:', error)
  }
}

onUnmounted(async () => {
  console.log(`🔄 交易面板窗口 ${windowId.value} 关闭，开始清理资源...`)

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown)

  // 使用统一的清理函数来移除合约行情数据获取
  try {
    // 在组件卸载时不显示用户消息，避免在窗口已关闭时显示消息
    await cleanupCurrentSubscription(false)
    console.log(`✅ 窗口 ${windowId.value} 的合约行情数据获取已完全移除`)
  } catch (error) {
    console.error(`❌ 清理窗口 ${windowId.value} 的合约行情订阅时发生异常:`, error)
    // 在组件卸载时不显示错误消息，因为窗口可能已经关闭
  }

  // 清理该窗口的所有资源
  if (windowId.value) {
    ctpService.cleanupWindow(windowId.value)
  }

  console.log(`✅ 交易面板窗口 ${windowId.value} 关闭，所有资源清理完成`)
})
</script>

<style scoped>
.trading-panel-container {
  padding: 10px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background: #f0f0f0;
}

/* 主交易面板样式 */
.trading-panel {
  display: flex;
  gap: 2px;
  background: #c0c0c0;
  border: 1px solid #808080;
  padding: 2px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  height: calc(100% - 50px);
  width: 100%;
  box-sizing: border-box;
}

/* 左侧操作列 */
.left-control-panel {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 3px;
  overflow-y: auto;
  flex-shrink: 0;
}

/* 合约代码显示 */
.contract-code-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px 4px;
  text-align: center;
  font-weight: bold;
  font-size: 10px;
  color: #000000;
}



/* 缩放控制 */
.zoom-controls {
  display: flex;
  gap: 1px;
  justify-content: center;
}

.zoom-btn {
  width: 16px;
  height: 16px;
  border: 1px outset #c0c0c0;
  background: #c0c0c0;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:active {
  border: 1px inset #c0c0c0;
}

/* 交易操作按钮 */
.trading-controls {
  margin-top: 8px;
  display: flex;
  gap: 4px;
}

.order-btn {
  width: 35px;
  height: 20px;
  border: 1px solid #666;
  color: white;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.place-btn {
  background: #dc3545; /* 红色 - 下单 */
}

.place-btn:hover {
  background: #c82333;
  border-color: #555;
}

.place-btn:active {
  background: #bd2130;
}

.cancel-btn {
  background: #28a745; /* 绿色 - 撤单 */
}

.cancel-btn:hover:not(:disabled) {
  background: #218838;
  border-color: #555;
}

.cancel-btn:active:not(:disabled) {
  background: #1e7e34;
}

.cancel-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 合约详情样式 */
.contract-details {
  margin-top: 10px;
  padding: 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 11px;
}

.contract-title {
  font-weight: bold;
  font-size: 12px;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e9ecef;
}

.contract-info-grid {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

.info-row .label {
  color: #6c757d;
  font-size: 10px;
  min-width: 50px;
}

.info-row .value {
  color: #495057;
  font-weight: 500;
  font-size: 10px;
  text-align: right;
}

.info-row .value.price {
  color: #2c3e50;
  font-weight: bold;
}

.info-row .value.change.positive {
  color: #dc3545;
}

.info-row .value.change.negative {
  color: #28a745;
}

.info-row .value.change.neutral {
  color: #6c757d;
}

/* 五档行情样式 */
.market-depth {
  margin-top: 8px;
  padding: 6px;
  background: #e8f4fd;
  border: 1px solid #b3d9f7;
  border-radius: 3px;
  font-size: 10px;
}

.depth-title {
  font-weight: bold;
  font-size: 11px;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 4px;
  padding-bottom: 2px;
  border-bottom: 1px solid #d1ecf1;
}

.depth-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1px 0;
  margin: 1px 0;
}

.depth-label {
  color: #6c757d;
  font-size: 9px;
  min-width: 30px;
}

.depth-price {
  font-weight: bold;
  font-size: 10px;
  font-family: 'Courier New', monospace;
}

.ask-price {
  color: #dc3545; /* 红色 - 卖价 */
}

.bid-price {
  color: #28a745; /* 绿色 - 买价 */
}

.depth-volume {
  color: #6c757d;
  font-size: 9px;
  text-align: right;
  min-width: 40px;
}

.depth-note {
  margin-top: 3px;
  padding-top: 2px;
  border-top: 1px solid #d1ecf1;
  text-align: center;
}

.note-text {
  color: #007bff;
  font-size: 8px;
  font-style: italic;
}

/* 订单状态样式 */
.order-status {
  margin-top: 8px;
  padding: 6px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 3px;
  font-size: 10px;
}

.status-title {
  font-weight: bold;
  font-size: 11px;
  color: #856404;
  text-align: center;
  margin-bottom: 4px;
  padding-bottom: 2px;
  border-bottom: 1px solid #ffeaa7;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1px 0;
  margin: 1px 0;
}

.status-label {
  color: #856404;
  font-size: 9px;
  min-width: 30px;
}

.status-value {
  font-weight: bold;
  font-size: 9px;
  font-family: 'Courier New', monospace;
  text-align: right;
  min-width: 40px;
}

.status-submitted {
  color: #007bff;
}

.status-partial {
  color: #fd7e14;
}

.status-filled {
  color: #28a745;
}

.status-cancelled {
  color: #6c757d;
}

.status-rejected {
  color: #dc3545;
}

.status-timeout {
  color: #ffc107;
}

.status-rejecting_cancel {
  color: #e83e8c;
}

.status-unknown {
  color: #6c757d;
}

/* 价格信息区域 */
.price-info-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price-change-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px;
  text-align: center;
}

.price-change-value {
  font-size: 10px;
  font-weight: bold;
}

.price-change-value.negative {
  color: #ff0000;
}

.price-change-value.positive {
  color: #008000;
}

/* 市场统计数据 */
.market-stats {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.stat-item {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 2px;
  text-align: center;
  font-size: 9px;
  color: #000000;
}

/* 合约信息样式 */
.contract-info {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  margin: 5px 0;
  font-size: 11px;
}

.contract-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px 0;
}

.contract-detail .label {
  color: #666;
  font-weight: 500;
  min-width: 45px;
}

.contract-detail .value {
  color: #333;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.contract-detail .value.price {
  color: #007bff;
}

.contract-detail .value.change.positive {
  color: #dc3545;
}

.contract-detail .value.change.negative {
  color: #28a745;
}

.contract-detail .value.change.neutral {
  color: #6c757d;
}



/* 红蓝数值显示 */
.zero-values {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.zero-value {
  height: 18px;
  line-height: 18px;
  text-align: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border: 1px inset #c0c0c0;
}

.zero-value.red {
  background: #ff0000;
}

.zero-value.blue {
  background: #0000ff;
}

/* 下单输入框 */
.order-inputs {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group {
  display: flex;
  justify-content: center;
}

.order-input {
  width: 60px;
  height: 18px;
  padding: 1px 3px;
  border: 1px inset #c0c0c0;
  background: #ffffff;
  text-align: center;
  font-size: 10px;
  color: #000000;
  font-family: 'MS Sans Serif', sans-serif;
}

.order-input:focus {
  outline: none;
  border: 1px inset #0000ff;
}

/* 去掉input number类型的上下箭头 */
.order-input::-webkit-outer-spin-button,
.order-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox浏览器去掉上下箭头 */
.order-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* 订单类型选择 */
.order-type-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.radio-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.radio-text {
  user-select: none;
}

/* 交易选项 */
.order-options {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.checkbox-text {
  user-select: none;
}

/* 持仓信息 */
.position-info-section {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.position-line {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 3px;
  font-size: 9px;
  color: #000000;
  text-align: left;
}

/* 盈亏显示 */
.pnl-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 3px;
  margin-top: auto;
}

.pnl-value {
  font-size: 12px;
  font-weight: bold;
  color: #000000;
}

.pnl-letter {
  font-size: 14px;
  font-weight: bold;
  color: #000000;
}

/* 右侧表格区域 */
.price-table-container {
  flex: 1;
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  display: flex;
  flex-direction: column;
  min-width: 0;
  max-width: 170px; /* 增加宽度以容纳5列 */
}

.price-table {
  flex: 1;
  overflow: hidden; /* 禁用滚动 */
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 卖盘和买盘区域 */
.sell-orders-section {
  flex: 1;
  background: #c0c0c0;
  display: flex;
  flex-direction: column; /* 卖盘正常排列，数据已在逻辑层倒序 */
  overflow: hidden;
  border-top: 1px solid #808080;
}



.buy-orders-section {
  flex: 1;
  background: #c0c0c0;
  display: flex;
  flex-direction: column; /* 买盘从上往下排列，最高价在顶部 */
  overflow: hidden;
  border-bottom: 1px solid #808080;
}



.price-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr; /* 五列等宽 */
  font-size: v-bind(fontSize + 'px');
  font-family: 'MS Sans Serif', sans-serif;
  background-color: #c0c0c0;
  align-items: center; /* 垂直居中对齐 */
  height: 18px; /* 默认高度18px */
  min-height: 18px;
  >div {
    height: 18px; /* 默认高度18px */
    min-height: 18px;
    text-align: center;
  }
}

/* 中间分隔线（黑色横条固定居中） */
.price-separator {
  height: 2px;
  background: #000000;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  border-left: 1px solid #808080;
  border-right: 1px solid #808080;
  flex-shrink: 0; /* 防止被压缩 */
  z-index: 10; /* 确保在最上层 */
}

/* 卖盘行样式 - 只有价格列显示红色 */
.sell-row .price-col {
  background: #ff0000; /* 价格列红色背景 */
  color: #ffffff;
}

/* 买盘行样式 - 只有价格列显示蓝色 */
.buy-row .price-col {
  background: #0000ff; /* 价格列蓝色背景 */
  color: #ffffff;
}

/* 表格列样式 */
.cancel-col {
  text-align: center;
  border-left: 1px solid #808080;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  background: #c0c0c0;
  cursor: pointer;
  font-weight: bold;
  color: #000000;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-col:hover {
  background: #a0a0a0;
}

/* 买量列 */
.buy-volume-col {
  text-align: center;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  cursor: pointer;
  color: #000000;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

.buy-volume-col:hover {
  background: rgba(0, 0, 255, 0.1);
}

/* 卖量列 */
.sell-volume-col {
  text-align: center;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  cursor: pointer;
  color: #000000;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

.sell-volume-col:hover {
  background: rgba(255, 0, 0, 0.1);
}

/* 总量列 */
.total-volume-col {
  text-align: center;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  border-right: 1px solid #808080;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  background: inherit;
}

/* 移除当前价格行样式，使用中间黑线分隔 */

.clickable {
  cursor: pointer;
  transition: all 0.1s ease;
}

.clickable.active {
  background: #008000 !important;
  color: #ffffff !important;
  font-weight: bold;
  border: 1px inset #008000 !important;
}





/* 滚动条样式 */
.price-table::-webkit-scrollbar,
.left-control-panel::-webkit-scrollbar {
  width: 5px;
}

.price-table::-webkit-scrollbar-track,
.left-control-panel::-webkit-scrollbar-track {
  background: #c0c0c0;
  border-radius: 4px;
}

.price-table::-webkit-scrollbar-thumb,
.left-control-panel::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 4px;
}

.price-table::-webkit-scrollbar-thumb:hover,
.left-control-panel::-webkit-scrollbar-thumb:hover {
  background: #606060;
}
</style>
